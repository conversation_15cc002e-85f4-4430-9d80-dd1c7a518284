import Controller from "@ember/controller";
import { action } from "@ember/object";
import { tracked } from "@glimmer/tracking";
import { inject as service } from "@ember/service";
import { ajax } from "discourse/lib/ajax";
import { popupAjaxError } from "discourse/lib/ajax-error";

export default class StudentVerificationController extends Controller {
  @service currentUser;
  @service router;

  @tracked email = "";
  @tracked isLoading = false;
  @tracked verificationStatus = null;
  @tracked message = "";
  @tracked messageType = "";

  init() {
    super.init(...arguments);
    this.loadVerificationStatus();
  }

  @action
  async loadVerificationStatus() {
    try {
      const response = await ajax("/student-verification/status");
      this.verificationStatus = response.status;
    } catch (error) {
      console.error("Failed to load verification status:", error);
    }
  }

  @action
  async submitEmail() {
    if (!this.email || !this.isValidEmail(this.email)) {
      this.showMessage("Please enter a valid email address", "error");
      return;
    }

    this.isLoading = true;
    this.clearMessage();

    try {
      const response = await ajax("/student-verification/submit-email", {
        type: "POST",
        data: { email: this.email }
      });

      this.showMessage(response.message, "success");
      await this.loadVerificationStatus();
    } catch (error) {
      popupAjaxError(error);
    } finally {
      this.isLoading = false;
    }
  }

  @action
  async resendVerification() {
    this.isLoading = true;
    this.clearMessage();

    try {
      const response = await ajax("/student-verification/resend-verification", {
        type: "POST"
      });

      this.showMessage(response.message, "success");
      await this.loadVerificationStatus();
    } catch (error) {
      popupAjaxError(error);
    } finally {
      this.isLoading = false;
    }
  }

  @action
  async verifyToken() {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");

    if (!token) {
      this.showMessage("No verification token found", "error");
      return;
    }

    this.isLoading = true;
    this.clearMessage();

    try {
      const response = await ajax("/student-verification/verify-token", {
        type: "POST",
        data: { token }
      });

      this.showMessage(response.message, "success");
      await this.loadVerificationStatus();
      
      // Redirect to user profile or dashboard after successful verification
      setTimeout(() => {
        this.router.transitionTo("user", this.currentUser.username);
      }, 2000);
    } catch (error) {
      popupAjaxError(error);
    } finally {
      this.isLoading = false;
    }
  }

  @action
  updateEmail(event) {
    this.email = event.target.value;
  }

  @action
  clearMessage() {
    this.message = "";
    this.messageType = "";
  }

  showMessage(message, type) {
    this.message = message;
    this.messageType = type;
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  get statusClass() {
    if (!this.verificationStatus) return "";
    
    switch (this.verificationStatus.status) {
      case "email_verified":
        return "verified";
      case "pending_email_verification":
      case "grace_period_active":
        return "pending";
      case "grace_period_expired":
        return "expired";
      default:
        return "";
    }
  }

  get statusIcon() {
    if (!this.verificationStatus) return "question-circle";
    
    switch (this.verificationStatus.status) {
      case "email_verified":
        return "check-circle";
      case "pending_email_verification":
        return "clock";
      case "grace_period_active":
        return "hourglass-half";
      case "grace_period_expired":
        return "times-circle";
      default:
        return "question-circle";
    }
  }

  get statusTitle() {
    if (!this.verificationStatus) return "Loading...";
    
    switch (this.verificationStatus.status) {
      case "email_verified":
        return "Email Verified";
      case "pending_email_verification":
        return "Pending Verification";
      case "grace_period_active":
        return "Grace Period Active";
      case "grace_period_expired":
        return "Grace Period Expired";
      default:
        return "Unknown Status";
    }
  }

  get statusDescription() {
    if (!this.verificationStatus) return "";
    
    switch (this.verificationStatus.status) {
      case "email_verified":
        return `Your university email ${this.verificationStatus.university_email} has been verified.`;
      case "pending_email_verification":
        return `Please check your email at ${this.verificationStatus.university_email} and click the verification link.`;
      case "grace_period_active":
        return `You have ${this.verificationStatus.days_remaining} days remaining to complete verification.`;
      case "grace_period_expired":
        return "Your grace period has expired. Please contact an administrator.";
      default:
        return "Please enter your university email to begin verification.";
    }
  }

  get showEmailForm() {
    return !this.verificationStatus || 
           !this.verificationStatus.status || 
           this.verificationStatus.status === "grace_period_expired";
  }

  get showResendButton() {
    return this.verificationStatus && 
           (this.verificationStatus.status === "pending_email_verification" ||
            this.verificationStatus.status === "grace_period_active");
  }

  get isVerified() {
    return this.verificationStatus && 
           this.verificationStatus.status === "email_verified";
  }
}
