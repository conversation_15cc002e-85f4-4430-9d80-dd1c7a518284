// Desktop-specific styles for Student Verification Plugin

.student-verification {
  &-container {
    max-width: 700px;
  }

  &-form {
    .form-group {
      display: flex;
      align-items: center;
      gap: 15px;

      label {
        min-width: 200px;
        margin-bottom: 0;
      }

      input[type="email"] {
        flex: 1;
      }
    }

    .btn-primary {
      min-width: 150px;
    }
  }

  &-status {
    .status-header {
      .status-icon {
        font-size: 28px;
      }

      .status-title {
        font-size: 20px;
      }
    }
  }

  &-modal {
    .modal-inner-container {
      max-width: 500px;
    }
  }

  // Hover effects for desktop
  &-form .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &-status:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
  }
}

// Admin interface desktop styles
.student-verification-admin {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }
}
