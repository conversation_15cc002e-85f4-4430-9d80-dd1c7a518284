# frozen_string_literal: true

class StudentVerificationMailer < ActionMailer::Base
  include Email::BuildEmailHelper

  def verification_email(user, university_email, verification_url, hours_until_expiry)
    @user = user
    @university_email = university_email
    @verification_url = verification_url
    @hours_until_expiry = hours_until_expiry
    @site_name = SiteSetting.title

    build_email(
      university_email,
      subject: SiteSetting.student_verification_email_subject,
      template: 'student_verification_email'
    )
  end

  private

  def build_email(to, opts = {})
    from_email = SiteSetting.student_verification_from_email.presence || SiteSetting.notification_email
    
    mail(
      to: to,
      from: from_email,
      subject: opts[:subject]
    ) do |format|
      format.html { render opts[:template] }
      format.text { render opts[:template] }
    end
  end
end
