# frozen_string_literal: true

class StudentVerification
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  # Verification status constants
  STATUS_GRACE_PERIOD_ACTIVE = 'grace_period_active'
  STATUS_PENDING_EMAIL_VERIFICATION = 'pending_email_verification'
  STATUS_EMAIL_VERIFIED = 'email_verified'
  STATUS_GRACE_PERIOD_EXPIRED = 'grace_period_expired'

  VALID_STATUSES = [
    STATUS_GRACE_PERIOD_ACTIVE,
    STATUS_PENDING_EMAIL_VERIFICATION,
    STATUS_EMAIL_VERIFIED,
    STATUS_GRACE_PERIOD_EXPIRED
  ].freeze

  attribute :user_id, :integer
  attribute :university_email, :string
  attribute :verified, :boolean, default: false
  attribute :status, :string
  attribute :deadline, :datetime
  attribute :token, :string
  attribute :token_expires_at, :datetime

  validates :user_id, presence: true
  validates :university_email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :status, inclusion: { in: VALID_STATUSES }

  def initialize(user)
    @user = user
    super(
      user_id: user.id,
      university_email: user.custom_fields['university_email_for_verification'],
      verified: user.custom_fields['university_email_verified'] == 'true',
      status: user.custom_fields['verification_status'],
      deadline: parse_datetime(user.custom_fields['verification_deadline']),
      token: user.custom_fields['email_verification_token'],
      token_expires_at: parse_datetime(user.custom_fields['email_verification_token_expires_at'])
    )
  end

  def user
    @user ||= User.find(user_id)
  end

  def save!
    user.custom_fields['university_email_for_verification'] = university_email
    user.custom_fields['university_email_verified'] = verified.to_s
    user.custom_fields['verification_status'] = status
    user.custom_fields['verification_deadline'] = deadline&.iso8601
    user.custom_fields['email_verification_token'] = token
    user.custom_fields['email_verification_token_expires_at'] = token_expires_at&.iso8601
    user.save_custom_fields(true)
  end

  def verified?
    verified == true
  end

  def grace_period_active?
    status == STATUS_GRACE_PERIOD_ACTIVE
  end

  def pending_email_verification?
    status == STATUS_PENDING_EMAIL_VERIFICATION
  end

  def email_verified?
    status == STATUS_EMAIL_VERIFIED
  end

  def grace_period_expired?
    status == STATUS_GRACE_PERIOD_EXPIRED || (deadline.present? && deadline < Time.current)
  end

  def token_valid?
    token.present? && token_expires_at.present? && token_expires_at > Time.current
  end

  def days_remaining
    return 0 unless deadline.present?

    days = ((deadline - Time.current) / 1.day).ceil
    [days, 0].max
  end

  def hours_until_token_expires
    return 0 unless token_expires_at.present?

    hours = ((token_expires_at - Time.current) / 1.hour).ceil
    [hours, 0].max
  end

  def generate_verification_token!
    self.token = SecureRandom.urlsafe_base64(32)
    self.token_expires_at = SiteSetting.student_verification_token_expiry_hours.hours.from_now
    save!
    token
  end

  def clear_verification_token!
    self.token = nil
    self.token_expires_at = nil
    save!
  end

  def start_grace_period!
    self.status = STATUS_GRACE_PERIOD_ACTIVE
    self.deadline = SiteSetting.student_verification_grace_period_days.days.from_now
    self.verified = false
    save!
  end

  def mark_email_verification_pending!
    self.status = STATUS_PENDING_EMAIL_VERIFICATION
    save!
  end

  def mark_email_verified!
    self.status = STATUS_EMAIL_VERIFIED
    self.verified = true
    clear_verification_token!
    save!

    # Remove user from grace period group when verified
    StudentVerificationService.remove_user_from_grace_period_group(user)
  end

  def mark_grace_period_expired!
    self.status = STATUS_GRACE_PERIOD_EXPIRED
    clear_verification_token!
    save!

    # Remove user from grace period group when expired
    StudentVerificationService.remove_user_from_grace_period_group(user)
  end

  def to_h
    {
      user_id: user_id,
      university_email: university_email,
      verified: verified,
      status: status,
      deadline: deadline,
      days_remaining: days_remaining,
      token_valid: token_valid?,
      hours_until_token_expires: hours_until_token_expires
    }
  end

  private

  def parse_datetime(value)
    return nil if value.blank?

    case value
    when String
      Time.parse(value) rescue nil
    when Time, DateTime
      value
    else
      nil
    end
  end
end
