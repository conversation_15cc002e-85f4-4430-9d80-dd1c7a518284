import Controller from "@ember/controller";
import { action } from "@ember/object";
import { tracked } from "@glimmer/tracking";
import { inject as service } from "@ember/service";
import { ajax } from "discourse/lib/ajax";

export default class PreferencesStudentVerificationController extends Controller {
  @service currentUser;
  @service siteSettings;
  @service flash;

  @tracked universityEmail = "";
  @tracked isLoading = false;

  init() {
    super.init(...arguments);
    // Initialize with existing university email if available
    this.universityEmail = this.currentUser.university_email_for_verification || "";
  }

  get showStudentVerification() {
    return this.siteSettings.student_verification_enabled;
  }

  @action
  updateUniversityEmail(event) {
    this.universityEmail = event.target.value;
  }

  @action
  async sendVerificationEmail() {
    // Get the university email value from the input field
    const universityEmail = this.universityEmail?.trim();

    // Basic validation
    if (!universityEmail) {
      this.flash.alert("Please enter a university email address.");
      return;
    }

    // Set loading state
    this.isLoading = true;

    try {
      // Make AJAX POST request to the backend endpoint
      const response = await ajax("/student-verification/send-email", {
        type: "POST",
        data: {
          university_email: universityEmail
        }
      });

      // On success, display success flash notification
      if (response.status === 'ok') {
        this.flash.success(`Verification email sent to ${universityEmail}. Please check your inbox.`);
      } else {
        // Handle unexpected response format
        this.flash.alert("Unexpected response from server. Please try again.");
      }

    } catch (error) {
      // On error, display error flash notification
      if (error.jqXHR && error.jqXHR.responseJSON && error.jqXHR.responseJSON.message) {
        // Use the specific error message from the server
        this.flash.alert(`Failed to send verification email: ${error.jqXHR.responseJSON.message}`);
      } else {
        // Use generic error message
        this.flash.alert("Failed to send verification email. Please try again.");
      }

      // Log the full error for debugging
      console.error("Verification email error:", error);
    } finally {
      // Reset loading state
      this.isLoading = false;
    }
  }
}
