# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UniversityEmailValidator do
  describe '.valid_university_email?' do
    context 'with valid university emails' do
      it 'accepts .edu domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(true)
      end

      it 'accepts .ac.uk domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(true)
      end

      it 'accepts .edu.au domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(true)
      end

      it 'accepts .ac.nz domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(true)
      end
    end

    context 'with invalid emails' do
      it 'rejects non-university domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(false)
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(false)
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(false)
      end

      it 'rejects invalid email formats' do
        expect(UniversityEmailValidator.valid_university_email?('invalid-email')).to eq(false)
        expect(UniversityEmailValidator.valid_university_email?('user@')).to eq(false)
        expect(UniversityEmailValidator.valid_university_email?('@university.edu')).to eq(false)
      end

      it 'rejects empty or nil emails' do
        expect(UniversityEmailValidator.valid_university_email?('')).to eq(false)
        expect(UniversityEmailValidator.valid_university_email?(nil)).to eq(false)
      end
    end

    context 'with custom domain configuration' do
      before do
        SiteSetting.student_verification_allowed_university_domains = 'custom.edu|special.ac.uk'
      end

      it 'accepts configured domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(true)
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(true)
      end

      it 'rejects non-configured domains' do
        expect(UniversityEmailValidator.valid_university_email?('<EMAIL>')).to eq(false)
      end
    end
  end

  describe '.extract_domain' do
    it 'extracts domain from email' do
      expect(UniversityEmailValidator.extract_domain('<EMAIL>')).to eq('example.com')
    end

    it 'converts domain to lowercase' do
      expect(UniversityEmailValidator.extract_domain('<EMAIL>')).to eq('example.com')
    end

    it 'handles complex domains' do
      expect(UniversityEmailValidator.extract_domain('<EMAIL>')).to eq('sub.university.edu')
    end
  end

  describe '.validate_domain_patterns' do
    it 'validates correct patterns' do
      result = UniversityEmailValidator.validate_domain_patterns('edu|ac.uk|edu.au')
      expect(result[:valid]).to eq(true)
      expect(result[:errors]).to be_empty
    end

    it 'identifies invalid patterns' do
      result = UniversityEmailValidator.validate_domain_patterns('edu|[invalid|ac.uk')
      expect(result[:valid]).to eq(false)
      expect(result[:errors]).not_to be_empty
    end

    it 'handles empty patterns' do
      result = UniversityEmailValidator.validate_domain_patterns('')
      expect(result[:valid]).to eq(true)
    end
  end

  describe '.suggest_corrections' do
    it 'suggests university domains for common email providers' do
      suggestions = UniversityEmailValidator.suggest_corrections('<EMAIL>')
      expect(suggestions).to include('<EMAIL>')
    end

    it 'suggests corrections for common typos' do
      suggestions = UniversityEmailValidator.suggest_corrections('<EMAIL>')
      expect(suggestions).to include('student@edu')
    end

    it 'returns empty array for university emails' do
      suggestions = UniversityEmailValidator.suggest_corrections('<EMAIL>')
      expect(suggestions).to be_empty
    end
  end
end
