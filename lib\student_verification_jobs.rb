# frozen_string_literal: true

module Jobs
  class CleanupExpiredVerificationTokens < ::Jobs::Scheduled
    every 1.hour

    def execute(args)
      StudentVerificationService.cleanup_expired_tokens
    end
  end

  class MarkExpiredGracePeriods < ::Jobs::Scheduled
    every 1.hour

    def execute(args)
      StudentVerificationService.mark_expired_grace_periods
    end
  end

  class SendVerificationReminder < ::Jobs::Base
    def execute(args)
      user_id = args[:user_id]
      return unless user_id

      user = User.find_by(id: user_id)
      return unless user

      verification = StudentVerification.new(user)
      return unless verification.grace_period_active?
      return if verification.days_remaining > 2 # Only send reminder when 2 days or less remain

      # Send reminder email
      begin
        message = StudentVerificationMailer.reminder_email(
          user,
          verification.university_email,
          verification.days_remaining
        )
        Email::Sender.new(message, :student_verification_reminder).send
      rescue => e
        Rails.logger.error("Failed to send verification reminder: #{e.message}")
      end
    end
  end

  class ScheduleVerificationReminders < ::Jobs::Scheduled
    every 1.day

    def execute(args)
      # Find users in grace period with 2 days or less remaining
      User.joins(:user_custom_fields)
          .where(user_custom_fields: { 
            name: 'verification_status', 
            value: 'grace_period_active' 
          })
          .joins("INNER JOIN user_custom_fields ucf2 ON ucf2.user_id = users.id")
          .where("ucf2.name = 'verification_deadline'")
          .where("ucf2.value <= ?", 2.days.from_now.iso8601)
          .where("ucf2.value > ?", Time.current.iso8601)
          .find_each do |user|
        Jobs.enqueue(:send_verification_reminder, user_id: user.id)
      end
    end
  end
end
