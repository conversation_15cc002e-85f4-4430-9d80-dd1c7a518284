en:
  js:
    user:
      preferences:
        student_verification: "Student Verification"
        student_verification_email_label: "Enter University Email for Verification:"
        student_verification_email_placeholder: "Enter your university email address"
        student_verification_send_button: "Send Verification Email"
        student_verification_sending_button: "Sending..."

    student_verification:
      title: "Student Verification"
      description: "Verify your university email address to access student-only content"

      form:
        email_label: "University Email Address"
        email_placeholder: "Enter your university email address"
        submit_button: "Send Verification Email"
        resend_button: "Resend Verification Email"
        verify_button: "Verify"

      status:
        not_started: "Please enter your university email address to begin verification"
        grace_period_active: "You have %{days} days remaining to complete verification"
        pending_verification: "Check your email and click the verification link"
        verified: "Your university email has been verified"
        expired: "Your grace period has expired. Please contact an administrator"

      messages:
        email_sent: "Verification email sent to %{email}"
        email_verified: "Email verified successfully!"
        invalid_email: "Please enter a valid university email address"
        invalid_domain: "This email domain is not from an approved university"
        verification_required: "University email verification is required to access this content"

      modal:
        title: "University Email Verification Required"
        body: "To access this content, you need to verify your university email address."
        verify_now: "Verify Now"
        cancel: "Cancel"

      navigation:
        student_verification: "Student Verification"

      admin:
        title: "Student Verification Settings"
        users:
          verified_count: "Verified Students"
          pending_count: "Pending Verification"
          expired_count: "Expired Grace Period"
