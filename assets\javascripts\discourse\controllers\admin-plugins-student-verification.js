import Controller from "@ember/controller";
import { action } from "@ember/object";
import { tracked } from "@glimmer/tracking";
import { ajax } from "discourse/lib/ajax";
import { popupAjaxError } from "discourse/lib/ajax-error";

export default class AdminPluginsStudentVerificationController extends Controller {
  @tracked stats = null;
  @tracked isLoading = false;

  init() {
    super.init(...arguments);
    this.loadStats();
  }

  @action
  async loadStats() {
    this.isLoading = true;
    
    try {
      const response = await ajax("/admin/plugins/student-verification/stats");
      this.stats = response.stats;
    } catch (error) {
      popupAjaxError(error);
    } finally {
      this.isLoading = false;
    }
  }

  @action
  async cleanupExpiredTokens() {
    try {
      await ajax("/admin/plugins/student-verification/cleanup-tokens", {
        type: "POST"
      });
      
      this.loadStats();
      // Show success message
    } catch (error) {
      popupAjaxError(error);
    }
  }

  @action
  async markExpiredGracePeriods() {
    try {
      await ajax("/admin/plugins/student-verification/mark-expired", {
        type: "POST"
      });
      
      this.loadStats();
      // Show success message
    } catch (error) {
      popupAjaxError(error);
    }
  }

  get verificationRate() {
    if (!this.stats || this.stats.total_users === 0) return 0;
    return ((this.stats.verified_users / this.stats.total_users) * 100).toFixed(1);
  }
}
