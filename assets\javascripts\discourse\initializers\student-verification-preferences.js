import { withPlugin<PERSON><PERSON> } from "discourse/lib/plugin-api";

export default {
  name: "student-verification-preferences",
  
  initialize() {
    withPluginApi("0.8.31", (api) => {
      // Add the student verification route to the user preferences
      api.addUserMenuGlyph((widget) => {
        if (widget.siteSettings.student_verification_enabled) {
          return {
            label: "student_verification.title",
            className: "student-verification-link",
            icon: "graduation-cap",
            href: "/student-verification"
          };
        }
      });
    });
  }
};
