# frozen_string_literal: true

class StudentVerificationService
  class EmailSendError < StandardError; end
  class TokenExpiredError < StandardError; end
  class TokenInvalidError < StandardError; end
  class NoVerificationInProgressError < StandardError; end

  def initialize(user)
    @user = user
    @verification = StudentVerification.new(user)
  end

  def initiate_verification(email)
    @verification.university_email = email
    @verification.start_grace_period!
    @verification.mark_email_verification_pending!

    send_verification_email
  end

  def verify_token(provided_token)
    raise TokenInvalidError unless @verification.token.present?
    raise TokenExpiredError unless @verification.token_valid?
    raise TokenInvalidError unless secure_compare(@verification.token, provided_token)

    @verification.mark_email_verified!

    # Trigger any post-verification hooks
    DiscourseEvent.trigger(:student_email_verified, @user, @verification.university_email)
  end

  def resend_verification
    unless @verification.pending_email_verification? || @verification.grace_period_active?
      raise NoVerificationInProgressError
    end

    send_verification_email
  end

  def get_verification_status
    # Update status if grace period has expired
    if @verification.grace_period_expired? && !@verification.email_verified?
      @verification.mark_grace_period_expired!
    end

    @verification.to_h
  end

  def user_has_access_to_restricted_content?
    return true unless SiteSetting.student_verification_require_verification
    return true if @verification.email_verified?
    return true if @verification.grace_period_active? && !@verification.grace_period_expired?

    false
  end

  def self.cleanup_expired_tokens
    User.joins(:user_custom_fields)
        .where(user_custom_fields: { name: 'email_verification_token_expires_at' })
        .where("user_custom_fields.value < ?", Time.current.iso8601)
        .find_each do |user|
      verification = StudentVerification.new(user)
      verification.clear_verification_token!
    end
  end

  def self.mark_expired_grace_periods
    User.joins(:user_custom_fields)
        .where(user_custom_fields: { name: 'verification_deadline' })
        .where("user_custom_fields.value < ?", Time.current.iso8601)
        .joins("INNER JOIN user_custom_fields ucf2 ON ucf2.user_id = users.id")
        .where("ucf2.name = 'verification_status' AND ucf2.value != 'email_verified'")
        .find_each do |user|
      verification = StudentVerification.new(user)
      verification.mark_grace_period_expired!
    end
  end

  def self.get_verification_stats
    total_users = User.count

    verified_users = User.joins(:user_custom_fields)
                        .where(user_custom_fields: {
                          name: 'university_email_verified',
                          value: 'true'
                        }).count

    pending_users = User.joins(:user_custom_fields)
                       .where(user_custom_fields: {
                         name: 'verification_status',
                         value: ['grace_period_active', 'pending_email_verification']
                       }).count

    expired_users = User.joins(:user_custom_fields)
                       .where(user_custom_fields: {
                         name: 'verification_status',
                         value: 'grace_period_expired'
                       }).count

    {
      total_users: total_users,
      verified_users: verified_users,
      pending_users: pending_users,
      expired_users: expired_users,
      verification_rate: total_users > 0 ? (verified_users.to_f / total_users * 100).round(2) : 0
    }
  end

  def self.ensure_grace_period_group_exists
    grace_period_group = Group.find_by(name: 'grace_period_users')

    unless grace_period_group
      begin
        grace_period_group = Group.create!(
          name: 'grace_period_users',
          title: 'Students in Grace Period',
          bio_raw: 'Users who are in their grace period for student verification',
          visibility_level: Group.visibility_levels[:staff],
          mentionable_level: Group::ALIAS_LEVELS[:nobody],
          messageable_level: Group::ALIAS_LEVELS[:nobody],
          automatic: false,
          public_admission: false,
          public_exit: false
        )
        Rails.logger.info("Created grace_period_users group for student verification")
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error("Failed to create grace_period_users group: #{e.message}")
        # Try to find it again in case it was created by another process
        grace_period_group = Group.find_by(name: 'grace_period_users')
      end
    end

    grace_period_group
  end

  def self.add_user_to_grace_period_group(user)
    grace_period_group = ensure_grace_period_group_exists

    if grace_period_group
      begin
        grace_period_group.add(user)
        Rails.logger.info("Added user #{user.username} (ID: #{user.id}) to grace_period_users group")
        true
      rescue => e
        Rails.logger.error("Failed to add user #{user.username} (ID: #{user.id}) to grace_period_users group: #{e.message}")
        false
      end
    else
      Rails.logger.error("Could not create or find grace_period_users group")
      false
    end
  end

  def self.remove_user_from_grace_period_group(user)
    grace_period_group = Group.find_by(name: 'grace_period_users')

    if grace_period_group
      begin
        grace_period_group.remove(user)
        Rails.logger.info("Removed user #{user.username} (ID: #{user.id}) from grace_period_users group")
        true
      rescue => e
        Rails.logger.error("Failed to remove user #{user.username} (ID: #{user.id}) from grace_period_users group: #{e.message}")
        false
      end
    else
      Rails.logger.warn("grace_period_users group not found when trying to remove user #{user.username}")
      false
    end
  end

  def self.ensure_verified_students_group_exists
    verified_students_group = Group.find_by(name: 'verified_students')

    unless verified_students_group
      begin
        verified_students_group = Group.create!(
          name: 'verified_students',
          title: 'Verified Students',
          bio_raw: 'Users who have successfully verified their university email addresses',
          visibility_level: Group.visibility_levels[:staff],
          mentionable_level: Group::ALIAS_LEVELS[:nobody],
          messageable_level: Group::ALIAS_LEVELS[:nobody],
          automatic: false,
          public_admission: false,
          public_exit: false
        )
        Rails.logger.info("Created verified_students group for student verification")
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error("Failed to create verified_students group: #{e.message}")
        # Try to find it again in case it was created by another process
        verified_students_group = Group.find_by(name: 'verified_students')
      end
    end

    verified_students_group
  end

  def self.add_user_to_verified_students_group(user)
    verified_students_group = ensure_verified_students_group_exists

    if verified_students_group
      begin
        verified_students_group.add(user)
        Rails.logger.info("Added user #{user.username} (ID: #{user.id}) to verified_students group")
        true
      rescue => e
        Rails.logger.error("Failed to add user #{user.username} (ID: #{user.id}) to verified_students group: #{e.message}")
        false
      end
    else
      Rails.logger.error("Could not create or find verified_students group")
      false
    end
  end

  private

  def send_verification_email
    token = @verification.generate_verification_token!
    verification_url = build_verification_url(token)

    email_subject = SiteSetting.student_verification_email_subject
    from_email = SiteSetting.student_verification_from_email.presence || SiteSetting.notification_email

    begin
      message = StudentVerificationMailer.verification_email(
        @user,
        @verification.university_email,
        verification_url,
        @verification.hours_until_token_expires
      )

      Email::Sender.new(message, :student_verification).send
    rescue => e
      Rails.logger.error("Failed to send student verification email: #{e.message}")
      raise EmailSendError
    end
  end

  def build_verification_url(token)
    "#{Discourse.base_url}/student-verification/verify?token=#{token}"
  end

  def secure_compare(a, b)
    return false unless a.bytesize == b.bytesize

    l = a.unpack("C*")
    r = b.unpack("C*")

    result = 0
    l.zip(r) { |x, y| result |= x ^ y }
    result == 0
  end
end
