# Discourse Student Verification Plugin

A comprehensive Discourse plugin that allows students to verify their university email addresses for access to student-only content and features.

## Features

- **University Email Verification**: Students can verify their university email addresses through a secure token-based system
- **Grace Period Management**: Configurable grace period for new users to complete verification
- **Flexible Domain Configuration**: Support for custom university domain patterns
- **Category Restrictions**: Restrict specific categories to verified students only
- **Automated Cleanup**: Background jobs handle token expiry and grace period management
- **Admin Dashboard**: Statistics and management tools for administrators
- **Responsive Design**: Mobile-friendly interface with touch-optimized interactions
- **Internationalization**: Full i18n support with English translations included

## Installation

1. Add the plugin to your Discourse installation:
   ```bash
   cd /var/discourse
   git clone https://github.com/discourse/discourse-student-verification.git plugins/discourse-student-verification
   ```

2. Rebuild your Discourse container:
   ```bash
   ./launcher rebuild app
   ```

## Configuration

### Site Settings

Navigate to Admin → Settings → Plugins → Student Verification to configure:

- **Enable Plugin**: `student_verification_enabled` (default: true)
- **Grace Period**: `student_verification_grace_period_days` (default: 7 days)
- **Token Expiry**: `student_verification_token_expiry_hours` (default: 24 hours)
- **University Domains**: `student_verification_allowed_university_domains` (pipe-separated patterns)
- **Require Verification**: `student_verification_require_verification` (default: false)
- **Restricted Categories**: `student_verification_restricted_categories` (category list)
- **Email Settings**: Custom subject and from address for verification emails

### University Domain Patterns

Configure allowed university domains using regex patterns separated by pipes:

```
edu|ac.uk|edu.au|ac.nz|edu.ca
```

The plugin includes comprehensive default patterns for universities worldwide.

## Usage

### For Students

1. Navigate to `/student-verification` or click the graduation cap icon in the user menu
2. Enter your university email address
3. Check your email for the verification link
4. Click the verification link to complete the process

### For Administrators

- View verification statistics in the admin panel
- Configure restricted categories that require verification
- Manage grace periods and token expiry settings
- Monitor verification rates and user status

## Custom User Fields

The plugin registers the following custom user fields:

- `university_email_for_verification` (string): University email to be verified
- `university_email_verified` (boolean): Verification status
- `verification_status` (string): Current verification state
- `verification_deadline` (datetime): Grace period expiry
- `email_verification_token` (string): Verification token (server-side only)
- `email_verification_token_expires_at` (datetime): Token expiry (server-side only)

## API Endpoints

- `POST /student-verification/submit-email`: Submit university email for verification
- `POST /student-verification/verify-token`: Verify email with token
- `POST /student-verification/resend-verification`: Resend verification email
- `GET /student-verification/status`: Get current verification status

## Background Jobs

The plugin includes several background jobs:

- **CleanupExpiredVerificationTokens**: Runs hourly to clean up expired tokens
- **MarkExpiredGracePeriods**: Runs hourly to mark expired grace periods
- **ScheduleVerificationReminders**: Runs daily to schedule reminder emails
- **SendVerificationReminder**: Sends reminder emails to users with expiring grace periods

## Security Features

- Secure token generation using `SecureRandom.urlsafe_base64`
- Constant-time token comparison to prevent timing attacks
- Rate limiting on verification attempts
- Token expiry for time-limited verification
- Server-side only storage of sensitive verification tokens

## Customization

### Styling

The plugin includes comprehensive SCSS files for styling:

- `assets/stylesheets/common/student-verification.scss`: Common styles
- `assets/stylesheets/desktop/student-verification.scss`: Desktop-specific styles
- `assets/stylesheets/mobile/student-verification.scss`: Mobile-specific styles

### Email Templates

Customize verification emails by modifying:

- `app/views/student_verification_mailer/student_verification_email.html.erb`
- `app/views/student_verification_mailer/student_verification_email.text.erb`

### Translations

Add translations by creating locale files in `config/locales/`:

- `server.{locale}.yml`: Server-side translations
- `client.{locale}.yml`: Client-side translations

## Testing

Run the test suite:

```bash
bundle exec rspec plugins/discourse-student-verification/spec
```

The plugin includes comprehensive tests for:

- Model validations and methods
- Email validation logic
- Service class functionality
- Controller endpoints

## Development

### File Structure

```
discourse-student-verification/
├── plugin.rb                          # Main plugin file
├── README.md                          # Documentation
├── config/
│   ├── settings.yml                   # Site settings
│   └── locales/                       # Translations
├── app/
│   ├── controllers/                   # API controllers
│   ├── models/                        # Data models
│   ├── mailers/                       # Email handling
│   └── views/                         # Email templates
├── lib/                               # Core logic
├── assets/
│   ├── javascripts/                   # Frontend code
│   └── stylesheets/                   # CSS/SCSS
└── spec/                              # Tests
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This plugin is released under the MIT License.

## Support

For issues and feature requests, please use the GitHub issue tracker.

## Changelog

### Version 0.1.0

- Initial release
- University email verification system
- Grace period management
- Category restrictions
- Admin dashboard
- Mobile-responsive design
- Comprehensive test suite
