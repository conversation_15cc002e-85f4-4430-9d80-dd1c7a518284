# frozen_string_literal: true

module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def can_access_student_content?
    return true unless SiteSetting.student_verification_enabled
    return true unless SiteSetting.student_verification_require_verification
    return true if is_admin? || is_moderator?
    return false unless authenticated?

    service = StudentVerificationService.new(user)
    service.user_has_access_to_restricted_content?
  end

  def can_access_category_with_student_verification?(category)
    return true unless SiteSetting.student_verification_enabled
    return true if is_admin? || is_moderator?
    return true unless authenticated?

    restricted_category_ids = SiteSetting.student_verification_restricted_categories
                                        .split('|')
                                        .map(&:to_i)
    
    return true unless restricted_category_ids.include?(category.id)

    service = StudentVerificationService.new(user)
    service.user_has_access_to_restricted_content?
  end

  def can_manage_student_verification?
    is_admin? || is_moderator?
  end
end

# Extend Guardian with student verification methods
Guardian.prepend(StudentVerificationGuardian)
