plugins:
  student_verification_enabled:
    default: true
    client: true
    description: "Enable student verification functionality"
  
  student_verification_grace_period_days:
    default: 7
    min: 1
    max: 30
    description: "Number of days students have to complete verification before losing access"
  
  student_verification_token_expiry_hours:
    default: 24
    min: 1
    max: 168
    description: "Number of hours before verification tokens expire"
  
  student_verification_allowed_university_domains:
    type: list
    default: "edu|ac.uk|edu.au|ac.nz"
    description: "Allowed university email domains (pipe-separated regex patterns)"
  
  student_verification_require_verification:
    default: false
    client: true
    description: "Require all new users to verify a university email address"
  
  student_verification_restricted_categories:
    type: category_list
    default: ""
    description: "Categories that require student verification to access"
  
  student_verification_email_subject:
    default: "Verify your university email address"
    description: "Subject line for verification emails"
  
  student_verification_from_email:
    default: ""
    description: "From email address for verification emails (leave blank to use site default)"
