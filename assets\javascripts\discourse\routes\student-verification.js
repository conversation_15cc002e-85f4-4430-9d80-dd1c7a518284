import Route from "@ember/routing/route";
import { inject as service } from "@ember/service";

export default class StudentVerificationRoute extends Route {
  @service currentUser;
  @service router;

  beforeModel() {
    if (!this.currentUser) {
      this.router.transitionTo("login");
      return;
    }

    if (!this.siteSettings.student_verification_enabled) {
      this.router.transitionTo("discovery.latest");
      return;
    }
  }

  model() {
    // Check if we're handling a verification token
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");
    
    return {
      token: token,
      user: this.currentUser
    };
  }

  setupController(controller, model) {
    super.setupController(controller, model);
    
    // If there's a token in the URL, automatically attempt verification
    if (model.token) {
      controller.verifyToken();
    }
  }
}
