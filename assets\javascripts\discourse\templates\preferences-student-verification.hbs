<div class="user-preferences-student-verification">
  <div class="control-group">
    <h3>{{i18n "js.user.preferences.student_verification"}}</h3>

    <div class="student-verification-form">
      <div class="form-group">
        <label for="university-email-input">
          {{i18n "js.user.preferences.student_verification_email_label"}}
        </label>
        <input
          type="email"
          id="university-email-input"
          class="university-email-input"
          placeholder={{i18n "js.user.preferences.student_verification_email_placeholder"}}
          value={{this.universityEmail}}
          {{on "input" this.updateUniversityEmail}}
        />
      </div>

      <div class="form-actions">
        <button
          type="button"
          class="btn btn-primary send-verification-btn"
          disabled={{this.isLoading}}
          {{on "click" this.sendVerificationEmail}}
        >
          {{#if this.isLoading}}
            {{d-icon "spinner" class="fa-spin"}}
            {{i18n "js.user.preferences.student_verification_sending_button"}}
          {{else}}
            {{i18n "js.user.preferences.student_verification_send_button"}}
          {{/if}}
        </button>
      </div>
    </div>
  </div>
</div>
