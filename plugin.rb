# frozen_string_literal: true

# name: discourse-student-verification
# about: Allows students to verify their university email addresses for access to student-only content
# version: 0.1.0
# authors: <PERSON><PERSON>
# url: https://github.com/discourse/discourse-student-verification
# required_version: 2.7.0
# transpile_js: true

enabled_site_setting :student_verification_enabled

register_asset "stylesheets/common/student-verification.scss"
register_asset "stylesheets/desktop/student-verification.scss", :desktop
register_asset "stylesheets/mobile/student-verification.scss", :mobile

# Register custom user fields for student verification
after_initialize do
  # Custom user field for storing the university email address to be verified
  # This field stores the email address that the student wants to verify
  register_editable_user_custom_field :university_email_for_verification

  # Boolean field to track if the university email has been successfully verified
  # Default value is false, set to true once verification is complete
  register_editable_user_custom_field :university_email_verified

  # String field to track the current verification state
  # Possible values: 'grace_period_active', 'pending_email_verification', 'email_verified', 'grace_period_expired'
  register_editable_user_custom_field :verification_status

  # DateTime field to store when the grace period expires
  # Students have a limited time to complete verification before losing access
  register_editable_user_custom_field :verification_deadline

  # String field to store the token sent via email for verification
  # This token is used to verify ownership of the university email address
  register_editable_user_custom_field :email_verification_token

  # DateTime field to store when the verification token expires
  # Tokens have a limited lifespan for security purposes
  register_editable_user_custom_field :email_verification_token_expires_at

  # Whitelist custom fields for user updates
  # This allows these fields to be updated through the user interface
  User.register_custom_field_type(:university_email_for_verification, :text)
  User.register_custom_field_type(:university_email_verified, :boolean)
  User.register_custom_field_type(:verification_status, :text)
  User.register_custom_field_type(:verification_deadline, :datetime)
  User.register_custom_field_type(:email_verification_token, :text)
  User.register_custom_field_type(:email_verification_token_expires_at, :datetime)

  # Add custom fields to user serializer so they're available in the frontend
  add_to_serializer(:user, :university_email_for_verification) do
    object.custom_fields['university_email_for_verification']
  end

  add_to_serializer(:user, :university_email_verified) do
    object.custom_fields['university_email_verified'] || false
  end

  add_to_serializer(:user, :verification_status) do
    object.custom_fields['verification_status']
  end

  add_to_serializer(:user, :verification_deadline) do
    object.custom_fields['verification_deadline']
  end

  # Don't expose sensitive fields like tokens in the serializer for security
  # These will only be accessible server-side

  # Load plugin components
  load File.expand_path("../app/controllers/student_verification_controller.rb", __FILE__)
  load File.expand_path("../app/models/student_verification.rb", __FILE__)
  load File.expand_path("../app/mailers/student_verification_mailer.rb", __FILE__)
  load File.expand_path("../lib/student_verification_service.rb", __FILE__)
  load File.expand_path("../lib/university_email_validator.rb", __FILE__)
  load File.expand_path("../lib/student_verification_guardian.rb", __FILE__)
  load File.expand_path("../lib/student_verification_jobs.rb", __FILE__)

  # Add routes for student verification
  Discourse::Application.routes.append do
    scope "/student-verification", defaults: { format: :json } do
      post "/submit-email" => "student_verification#submit_email"
      post "/send-email" => "student_verification#send_verification_email"
      post "/verify-token" => "student_verification#verify_token"
      post "/resend-verification" => "student_verification#resend_verification"
      get "/verify-email" => "student_verification#verify_email_from_link"
      get "/verify-email-token" => "student_verification#verify_email_token"
      get "/status" => "student_verification#status"
    end
  end

  # Add user preferences route
  add_to_class(:user, :student_verification_enabled?) do
    SiteSetting.student_verification_enabled
  end

  # Navigation is handled by JavaScript initializers and connectors
  # See assets/javascripts/discourse/initializers/student-verification-preferences.js
  # and assets/javascripts/discourse/connectors/user-preferences-nav/student-verification-nav.hbs

  # Register site settings
  register_site_setting_type(:university_domains, 'list')

  # Listen for user creation events
  DiscourseEvent.on(:user_created) do |user|
    # Only process if student verification is enabled
    next unless SiteSetting.student_verification_enabled

    begin
      # Set verification status to grace period active
      user.custom_fields['verification_status'] = 'grace_period_active'

      # Set verification deadline to 14 days from now
      user.custom_fields['verification_deadline'] = 14.days.from_now.iso8601

      # Set university email verified to false
      user.custom_fields['university_email_verified'] = 'false'

      # Save the custom fields
      user.save_custom_fields(true)

      # Add user to grace_period_users group (create group if it doesn't exist)
      StudentVerificationService.add_user_to_grace_period_group(user)

      Rails.logger.info("Initialized student verification for user #{user.username} (ID: #{user.id})")

    rescue => e
      Rails.logger.error("Failed to initialize student verification for user #{user.username} (ID: #{user.id}): #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
    end
  end
end



