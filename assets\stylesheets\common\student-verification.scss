// Student Verification Plugin Styles

.student-verification {
  &-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
  }

  &-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      color: var(--primary);
      margin-bottom: 10px;
    }

    .description {
      color: var(--primary-medium);
      font-size: 1.1em;
    }
  }

  &-form {
    background: var(--secondary);
    padding: 30px;
    border-radius: 8px;
    border: 1px solid var(--primary-low);
    margin-bottom: 20px;

    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        font-weight: bold;
        margin-bottom: 8px;
        color: var(--primary);
      }

      input[type="email"] {
        width: 100%;
        padding: 12px;
        border: 1px solid var(--primary-low);
        border-radius: 4px;
        font-size: 16px;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: var(--tertiary);
          box-shadow: 0 0 0 2px var(--tertiary-low);
        }
      }
    }

    .btn-primary {
      background: var(--tertiary);
      color: var(--secondary);
      border: none;
      padding: 12px 24px;
      border-radius: 4px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: var(--tertiary-hover);
      }

      &:disabled {
        background: var(--primary-low);
        color: var(--primary-medium);
        cursor: not-allowed;
      }
    }
  }

  &-status {
    background: var(--secondary);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--primary-low);
    margin-bottom: 20px;

    &.verified {
      border-color: var(--success);
      background: var(--success-low);

      .status-icon {
        color: var(--success);
      }
    }

    &.pending {
      border-color: var(--tertiary);
      background: var(--tertiary-low);

      .status-icon {
        color: var(--tertiary);
      }
    }

    &.expired {
      border-color: var(--danger);
      background: var(--danger-low);

      .status-icon {
        color: var(--danger);
      }
    }

    .status-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .status-icon {
        font-size: 24px;
        margin-right: 10px;
      }

      .status-title {
        font-size: 18px;
        font-weight: bold;
        margin: 0;
      }
    }

    .status-details {
      color: var(--primary-medium);
      line-height: 1.5;

      .countdown {
        font-weight: bold;
        color: var(--primary);
      }
    }
  }

  &-actions {
    text-align: center;
    margin-top: 20px;

    .btn {
      margin: 0 10px;
    }
  }

  &-modal {
    .modal-header {
      text-align: center;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--primary-low);
    }

    .modal-body {
      padding: 20px 0;
      text-align: center;
    }

    .modal-footer {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid var(--primary-low);
    }
  }

  // Alert styles
  &-alert {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid transparent;

    &.success {
      background: var(--success-low);
      border-color: var(--success);
      color: var(--success-hover);
    }

    &.error {
      background: var(--danger-low);
      border-color: var(--danger);
      color: var(--danger-hover);
    }

    &.info {
      background: var(--tertiary-low);
      border-color: var(--tertiary);
      color: var(--tertiary-hover);
    }

    &.warning {
      background: var(--highlight-low);
      border-color: var(--highlight-medium);
      color: var(--highlight-hover);
    }
  }

  // Navigation item
  .nav-item {
    .d-icon-graduation-cap {
      color: var(--tertiary);
    }
  }

  // User preferences section
  .user-preferences-student-verification {
    .control-group {
      margin-bottom: 30px;

      h3 {
        color: var(--primary);
        margin-bottom: 20px;
        font-size: 1.2em;
        font-weight: bold;
      }
    }

    .student-verification-form {
      background: var(--secondary);
      padding: 25px;
      border-radius: 8px;
      border: 1px solid var(--primary-low);

      .form-group {
        margin-bottom: 20px;

        label {
          display: block;
          font-weight: bold;
          margin-bottom: 8px;
          color: var(--primary);
          font-size: 14px;
        }

        .university-email-input {
          width: 100%;
          max-width: 400px;
          padding: 10px 12px;
          border: 1px solid var(--primary-low);
          border-radius: 4px;
          font-size: 14px;
          background: var(--secondary);
          color: var(--primary);
          box-sizing: border-box;

          &:focus {
            outline: none;
            border-color: var(--tertiary);
            box-shadow: 0 0 0 2px var(--tertiary-low);
          }

          &::placeholder {
            color: var(--primary-medium);
          }
        }
      }

      .form-actions {
        margin-top: 20px;

        .send-verification-btn {
          background: var(--tertiary);
          color: var(--secondary);
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;
          transition: background-color 0.2s ease;
          display: flex;
          align-items: center;
          gap: 8px;

          &:hover:not(:disabled) {
            background: var(--tertiary-hover);
          }

          &:active:not(:disabled) {
            background: var(--tertiary-high);
          }

          &:disabled {
            background: var(--primary-low);
            color: var(--primary-medium);
            cursor: not-allowed;
            opacity: 0.6;
          }

          .d-icon {
            font-size: 12px;
          }

          .fa-spin {
            animation: fa-spin 1s infinite linear;
          }
        }
      }
    }
  }

  // Preferences navigation
  .nav-student-verification {
    a {
      display: flex;
      align-items: center;
      gap: 8px;

      .d-icon {
        color: var(--tertiary);
      }
    }

    &.active a {
      background: var(--tertiary-low);
      color: var(--tertiary);

      .d-icon {
        color: var(--tertiary);
      }
    }
  }

  // Admin interface
  &-admin {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: var(--secondary);
      padding: 20px;
      border-radius: 8px;
      border: 1px solid var(--primary-low);
      text-align: center;

      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: var(--tertiary);
        display: block;
      }

      .stat-label {
        color: var(--primary-medium);
        margin-top: 5px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .student-verification {
    &-container {
      padding: 10px;
    }

    &-form {
      padding: 20px;
    }

    &-actions .btn {
      display: block;
      width: 100%;
      margin: 10px 0;
    }
  }
}
