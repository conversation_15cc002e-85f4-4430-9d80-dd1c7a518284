<div class="student-verification-container">
  <div class="student-verification-header">
    <h1>{{i18n "js.student_verification.title"}}</h1>
    <p class="description">{{i18n "js.student_verification.description"}}</p>
  </div>

  {{#if this.message}}
    <div class="student-verification-alert {{this.messageType}}">
      {{this.message}}
    </div>
  {{/if}}

  {{#if this.verificationStatus}}
    <div class="student-verification-status {{this.statusClass}}">
      <div class="status-header">
        {{d-icon this.statusIcon class="status-icon"}}
        <h3 class="status-title">{{this.statusTitle}}</h3>
      </div>
      <div class="status-details">
        <p>{{this.statusDescription}}</p>
        {{#if (eq this.verificationStatus.status "grace_period_active")}}
          <p class="countdown">
            {{i18n "js.student_verification.status.grace_period_active" days=this.verificationStatus.days_remaining}}
          </p>
        {{/if}}
      </div>
    </div>
  {{/if}}

  {{#if this.showEmailForm}}
    <div class="student-verification-form">
      <div class="form-group">
        <label for="university-email">{{i18n "js.student_verification.form.email_label"}}</label>
        <input
          type="email"
          id="university-email"
          placeholder={{i18n "js.student_verification.form.email_placeholder"}}
          value={{this.email}}
          {{on "input" this.updateEmail}}
          disabled={{this.isLoading}}
        />
      </div>
      
      <div class="student-verification-actions">
        <button
          class="btn btn-primary"
          {{on "click" this.submitEmail}}
          disabled={{or this.isLoading (not this.email)}}
        >
          {{#if this.isLoading}}
            {{d-icon "spinner" class="fa-spin"}}
          {{/if}}
          {{i18n "js.student_verification.form.submit_button"}}
        </button>
      </div>
    </div>
  {{/if}}

  {{#if this.showResendButton}}
    <div class="student-verification-actions">
      <button
        class="btn btn-default"
        {{on "click" this.resendVerification}}
        disabled={{this.isLoading}}
      >
        {{#if this.isLoading}}
          {{d-icon "spinner" class="fa-spin"}}
        {{/if}}
        {{i18n "js.student_verification.form.resend_button"}}
      </button>
    </div>
  {{/if}}

  {{#if this.isVerified}}
    <div class="student-verification-actions">
      <LinkTo @route="user" @model={{this.currentUser.username}} class="btn btn-primary">
        {{i18n "user.preferences.title"}}
      </LinkTo>
    </div>
  {{/if}}
</div>
