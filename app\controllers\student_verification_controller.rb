# frozen_string_literal: true

class StudentVerificationController < ApplicationController
  requires_login
  before_action :ensure_student_verification_enabled
  before_action :rate_limit_verification_attempts, only: [:submit_email, :resend_verification, :send_verification_email]

  def submit_email
    email = params[:email]&.strip&.downcase

    unless email.present?
      return render_json_error(I18n.t("student_verification.errors.invalid_email"))
    end

    unless UniversityEmailValidator.valid_university_email?(email)
      return render_json_error(I18n.t("student_verification.errors.invalid_domain"))
    end

    # Check if email is already verified by another user
    if User.joins(:user_custom_fields)
          .where(user_custom_fields: {
            name: 'university_email_for_verification',
            value: email
          })
          .where("user_custom_fields.user_id != ?", current_user.id)
          .joins("INNER JOIN user_custom_fields ucf2 ON ucf2.user_id = users.id")
          .where("ucf2.name = 'university_email_verified' AND ucf2.value = 'true'")
          .exists?
      return render_json_error(I18n.t("student_verification.errors.already_verified"))
    end

    begin
      service = StudentVerificationService.new(current_user)
      service.initiate_verification(email)

      render json: {
        success: true,
        message: I18n.t("student_verification.success.email_sent")
      }
    rescue StudentVerificationService::EmailSendError => e
      render_json_error(I18n.t("student_verification.errors.email_send_failed"))
    rescue => e
      Rails.logger.error("Student verification error: #{e.message}")
      render_json_error(I18n.t("student_verification.errors.email_send_failed"))
    end
  end

  def verify_token
    token = params[:token]

    unless token.present?
      return render_json_error(I18n.t("student_verification.errors.token_invalid"))
    end

    begin
      service = StudentVerificationService.new(current_user)
      service.verify_token(token)

      render json: {
        success: true,
        message: I18n.t("student_verification.success.email_verified")
      }
    rescue StudentVerificationService::TokenExpiredError
      render_json_error(I18n.t("student_verification.errors.token_expired"))
    rescue StudentVerificationService::TokenInvalidError
      render_json_error(I18n.t("student_verification.errors.token_invalid"))
    rescue => e
      Rails.logger.error("Token verification error: #{e.message}")
      render_json_error(I18n.t("student_verification.errors.token_invalid"))
    end
  end

  def resend_verification
    begin
      service = StudentVerificationService.new(current_user)
      service.resend_verification

      render json: {
        success: true,
        message: I18n.t("student_verification.success.email_sent")
      }
    rescue StudentVerificationService::NoVerificationInProgressError
      render_json_error("No verification in progress")
    rescue StudentVerificationService::EmailSendError
      render_json_error(I18n.t("student_verification.errors.email_send_failed"))
    rescue => e
      Rails.logger.error("Resend verification error: #{e.message}")
      render_json_error(I18n.t("student_verification.errors.email_send_failed"))
    end
  end

  def status
    service = StudentVerificationService.new(current_user)
    status_info = service.get_verification_status

    render json: {
      success: true,
      status: status_info
    }
  end

  def send_verification_email
    university_email = params[:university_email]&.strip&.downcase

    # Basic validation: ensure email is not blank
    unless university_email.present?
      return render json: {
        status: 'error',
        message: 'University email cannot be blank'
      }, status: 400
    end

    # Additional validation: check if it's a valid email format
    unless university_email.match?(URI::MailTo::EMAIL_REGEXP)
      return render json: {
        status: 'error',
        message: 'Please enter a valid email address'
      }, status: 400
    end

    begin
      # Generate a unique, secure random token (32 characters)
      verification_token = SecureRandom.urlsafe_base64(32)

      # Set token expiry to 24 hours from now
      token_expires_at = 24.hours.from_now

      # Store token and related data in user's custom fields
      current_user.custom_fields['email_verification_token'] = verification_token
      current_user.custom_fields['email_verification_token_expires_at'] = token_expires_at.iso8601
      current_user.custom_fields['university_email_for_verification'] = university_email
      current_user.custom_fields['verification_status'] = 'pending_email_verification'

      # Save the custom fields
      current_user.save_custom_fields(true)

      # Construct verification URL
      verification_url = "#{Discourse.base_url}/student-verification/verify-email-token?token=#{verification_token}"

      # Send email to the university email address
      send_verification_email_to_user(university_email, verification_url)

      # Return success response
      render json: {
        status: 'ok',
        message: 'Verification email sent.'
      }

    rescue => e
      Rails.logger.error("Failed to send verification email: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))

      render json: {
        status: 'error',
        message: 'Failed to send verification email. Please try again.'
      }, status: 500
    end
  end

  def verify_email_from_link
    token = params[:token]

    unless token.present?
      return redirect_to "/student-verification?error=invalid_token"
    end

    # Find user with this token
    user_with_token = User.joins(:user_custom_fields)
                         .where(user_custom_fields: {
                           name: 'email_verification_token',
                           value: token
                         }).first

    unless user_with_token
      return redirect_to "/student-verification?error=invalid_token"
    end

    # Check if token has expired
    token_expires_at = user_with_token.custom_fields['email_verification_token_expires_at']
    if token_expires_at.blank? || Time.parse(token_expires_at) < Time.current
      return redirect_to "/student-verification?error=token_expired"
    end

    begin
      # Mark email as verified
      user_with_token.custom_fields['university_email_verified'] = 'true'
      user_with_token.custom_fields['verification_status'] = 'email_verified'
      user_with_token.custom_fields['email_verification_token'] = nil
      user_with_token.custom_fields['email_verification_token_expires_at'] = nil
      user_with_token.save_custom_fields(true)

      # Remove user from grace period group
      StudentVerificationService.remove_user_from_grace_period_group(user_with_token)

      # Redirect to success page
      redirect_to "/student-verification?success=email_verified"

    rescue => e
      Rails.logger.error("Failed to verify email: #{e.message}")
      redirect_to "/student-verification?error=verification_failed"
    end
  end

  def verify_email_token
    token = params[:token]

    # Check if token parameter is provided
    unless token.present?
      flash[:error] = "Invalid verification link."
      return redirect_to "/"
    end

    # Find user whose email_verification_token matches the provided token
    user_with_token = User.joins(:user_custom_fields)
                         .where(user_custom_fields: {
                           name: 'email_verification_token',
                           value: token
                         }).first

    # If no user is found for the token
    unless user_with_token
      flash[:error] = "Invalid verification link."
      return redirect_to "/"
    end

    # Check if token has expired
    token_expires_at = user_with_token.custom_fields['email_verification_token_expires_at']
    if token_expires_at.blank? || Time.parse(token_expires_at) < Time.current
      flash[:error] = "Verification link is invalid or has expired. Please try again from your profile settings."
      return redirect_to "/"
    end

    begin
      # Set user's university_email_verified to true
      user_with_token.custom_fields['university_email_verified'] = 'true'

      # Set user's verification_status to email_verified
      user_with_token.custom_fields['verification_status'] = 'email_verified'

      # Clear the verification token fields
      user_with_token.custom_fields['email_verification_token'] = nil
      user_with_token.custom_fields['email_verification_token_expires_at'] = nil

      # Save the custom fields
      user_with_token.save_custom_fields(true)

      # Add user to verified_students group
      StudentVerificationService.add_user_to_verified_students_group(user_with_token)

      # Remove user from grace_period_users group
      StudentVerificationService.remove_user_from_grace_period_group(user_with_token)

      # Remove user from unverified_users group if they are in it
      remove_user_from_unverified_users_group(user_with_token)

      # Redirect to homepage with success message
      flash[:success] = "Student email successfully verified!"
      redirect_to "/"

    rescue => e
      Rails.logger.error("Failed to verify email for user #{user_with_token.username}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))

      flash[:error] = "Verification link is invalid or has expired. Please try again from your profile settings."
      redirect_to "/"
    end
  end

  private

  def ensure_student_verification_enabled
    unless SiteSetting.student_verification_enabled
      raise Discourse::NotFound
    end
  end

  def rate_limit_verification_attempts
    RateLimiter.new(current_user, "student_verification_#{current_user.id}", 5, 1.hour).performed!
  rescue RateLimiter::LimitExceeded
    render_json_error(I18n.t("student_verification.errors.rate_limited"))
  end

  def send_verification_email_to_user(university_email, verification_url)
    # Get forum name for subject
    forum_name = SiteSetting.title

    # Prepare email content
    subject = "Verify Your Student Email for #{forum_name}"

    body = <<~EMAIL_BODY
      Hello #{current_user.username},

      You have requested to verify your university email address (#{university_email}) for access to student-only content on #{forum_name}.

      Please click the link below to verify your email address:

      #{verification_url}

      This verification link will expire in 24 hours.

      If you didn't request this verification, you can safely ignore this email.

      Best regards,
      The #{forum_name} Team
    EMAIL_BODY

    # Send the email using Discourse's email system
    message = Mail.new do
      from    SiteSetting.notification_email
      to      university_email
      subject subject
      body    body
    end

    Email::Sender.new(message, :student_verification).send
  end

  def remove_user_from_unverified_users_group(user)
    unverified_users_group = Group.find_by(name: 'unverified_users')

    if unverified_users_group
      begin
        unverified_users_group.remove(user)
        Rails.logger.info("Removed user #{user.username} (ID: #{user.id}) from unverified_users group")
      rescue => e
        Rails.logger.error("Failed to remove user #{user.username} (ID: #{user.id}) from unverified_users group: #{e.message}")
      end
    else
      Rails.logger.debug("unverified_users group not found (this is normal if not using this group)")
    end
  end
end
