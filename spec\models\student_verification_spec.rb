# frozen_string_literal: true

require 'rails_helper'

RSpec.describe StudentVerification do
  let(:user) { Fabricate(:user) }
  let(:verification) { StudentVerification.new(user) }

  describe '#initialize' do
    it 'creates a verification object for a user' do
      expect(verification.user_id).to eq(user.id)
      expect(verification.verified).to eq(false)
    end
  end

  describe '#start_grace_period!' do
    it 'sets the status to grace_period_active' do
      verification.start_grace_period!
      expect(verification.status).to eq(StudentVerification::STATUS_GRACE_PERIOD_ACTIVE)
      expect(verification.deadline).to be_present
      expect(verification.verified).to eq(false)
    end
  end

  describe '#mark_email_verification_pending!' do
    it 'sets the status to pending_email_verification' do
      verification.mark_email_verification_pending!
      expect(verification.status).to eq(StudentVerification::STATUS_PENDING_EMAIL_VERIFICATION)
    end
  end

  describe '#mark_email_verified!' do
    it 'sets the status to email_verified and clears token' do
      verification.token = 'test_token'
      verification.mark_email_verified!

      expect(verification.status).to eq(StudentVerification::STATUS_EMAIL_VERIFIED)
      expect(verification.verified).to eq(true)
      expect(verification.token).to be_nil
    end
  end

  describe '#generate_verification_token!' do
    it 'generates a secure token with expiry' do
      token = verification.generate_verification_token!

      expect(token).to be_present
      expect(verification.token).to eq(token)
      expect(verification.token_expires_at).to be > Time.current
    end
  end

  describe '#token_valid?' do
    context 'when token exists and has not expired' do
      before do
        verification.token = 'valid_token'
        verification.token_expires_at = 1.hour.from_now
      end

      it 'returns true' do
        expect(verification.token_valid?).to eq(true)
      end
    end

    context 'when token has expired' do
      before do
        verification.token = 'expired_token'
        verification.token_expires_at = 1.hour.ago
      end

      it 'returns false' do
        expect(verification.token_valid?).to eq(false)
      end
    end

    context 'when no token exists' do
      it 'returns false' do
        expect(verification.token_valid?).to eq(false)
      end
    end
  end

  describe '#grace_period_expired?' do
    context 'when deadline has passed' do
      before do
        verification.deadline = 1.day.ago
      end

      it 'returns true' do
        expect(verification.grace_period_expired?).to eq(true)
      end
    end

    context 'when deadline has not passed' do
      before do
        verification.deadline = 1.day.from_now
      end

      it 'returns false' do
        expect(verification.grace_period_expired?).to eq(false)
      end
    end

    context 'when status is grace_period_expired' do
      before do
        verification.status = StudentVerification::STATUS_GRACE_PERIOD_EXPIRED
      end

      it 'returns true' do
        expect(verification.grace_period_expired?).to eq(true)
      end
    end
  end

  describe '#days_remaining' do
    context 'when deadline is in the future' do
      before do
        verification.deadline = 3.days.from_now
      end

      it 'returns the correct number of days' do
        expect(verification.days_remaining).to eq(3)
      end
    end

    context 'when deadline has passed' do
      before do
        verification.deadline = 1.day.ago
      end

      it 'returns 0' do
        expect(verification.days_remaining).to eq(0)
      end
    end
  end

  describe 'group management' do
    let(:grace_period_group) { Fabricate(:group, name: 'grace_period_users') }

    before do
      grace_period_group # ensure group exists
    end

    describe '#mark_email_verified!' do
      it 'removes user from grace period group' do
        grace_period_group.add(user)
        expect(grace_period_group.users).to include(user)

        verification.mark_email_verified!

        expect(grace_period_group.reload.users).not_to include(user)
      end
    end

    describe '#mark_grace_period_expired!' do
      it 'removes user from grace period group' do
        grace_period_group.add(user)
        expect(grace_period_group.users).to include(user)

        verification.mark_grace_period_expired!

        expect(grace_period_group.reload.users).not_to include(user)
      end
    end
  end
end
