// Mobile-specific styles for Student Verification Plugin

.student-verification {
  &-container {
    padding: 15px;
  }

  &-header {
    margin-bottom: 20px;

    h1 {
      font-size: 1.5em;
    }

    .description {
      font-size: 1em;
    }
  }

  &-form {
    padding: 20px 15px;

    .form-group {
      margin-bottom: 15px;

      label {
        font-size: 14px;
      }

      input[type="email"] {
        padding: 10px;
        font-size: 16px; // Prevents zoom on iOS
      }
    }

    .btn-primary {
      width: 100%;
      padding: 14px;
      font-size: 16px;
    }
  }

  &-status {
    padding: 15px;

    .status-header {
      flex-direction: column;
      text-align: center;
      margin-bottom: 10px;

      .status-icon {
        font-size: 32px;
        margin-right: 0;
        margin-bottom: 10px;
      }

      .status-title {
        font-size: 16px;
      }
    }

    .status-details {
      text-align: center;
      font-size: 14px;
    }
  }

  &-actions {
    .btn {
      display: block;
      width: 100%;
      margin: 10px 0;
      padding: 12px;
    }
  }

  &-modal {
    .modal-header,
    .modal-body,
    .modal-footer {
      padding: 15px;
    }

    .modal-header h3 {
      font-size: 1.2em;
    }
  }

  &-alert {
    padding: 12px;
    font-size: 14px;
  }

  // User preferences mobile styles
  .user-preferences-student-verification {
    .student-verification-form {
      padding: 20px 15px;

      .form-group {
        .university-email-input {
          max-width: 100%;
          font-size: 16px; // Prevents zoom on iOS
          padding: 12px;
        }
      }

      .form-actions {
        .send-verification-btn {
          width: 100%;
          padding: 14px;
          font-size: 16px;
        }
      }
    }
  }
}

// Admin interface mobile styles
.student-verification-admin {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-card {
    padding: 15px;

    .stat-number {
      font-size: 1.5em;
    }

    .stat-label {
      font-size: 14px;
    }
  }
}

// Touch-friendly interactions
@media (hover: none) and (pointer: coarse) {
  .student-verification {
    &-form .btn-primary:active {
      background: var(--tertiary-hover);
      transform: scale(0.98);
    }

    &-status:active {
      background: var(--primary-very-low);
    }
  }
}
