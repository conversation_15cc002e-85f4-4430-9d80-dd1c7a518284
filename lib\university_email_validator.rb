# frozen_string_literal: true

class UniversityEmailValidator
  # Common university domain patterns
  DEFAULT_UNIVERSITY_PATTERNS = [
    /\.edu$/i,                    # US educational institutions
    /\.ac\.uk$/i,                 # UK academic institutions
    /\.edu\.au$/i,                # Australian educational institutions
    /\.ac\.nz$/i,                 # New Zealand academic institutions
    /\.edu\.ca$/i,                # Canadian educational institutions
    /\.ac\.za$/i,                 # South African academic institutions
    /\.edu\.sg$/i,                # Singapore educational institutions
    /\.edu\.my$/i,                # Malaysian educational institutions
    /\.ac\.in$/i,                 # Indian academic institutions
    /\.edu\.br$/i,                # Brazilian educational institutions
    /\.ac\.jp$/i,                 # Japanese academic institutions
    /\.edu\.cn$/i,                # Chinese educational institutions
    /\.ac\.kr$/i,                 # Korean academic institutions
    /\.edu\.tw$/i,                # Taiwanese educational institutions
    /\.ac\.th$/i,                 # Thai academic institutions
    /\.edu\.ph$/i,                # Philippine educational institutions
    /\.ac\.id$/i,                 # Indonesian academic institutions
    /\.edu\.vn$/i,                # Vietnamese educational institutions
    /\.ac\.il$/i,                 # Israeli academic institutions
    /\.edu\.tr$/i,                # Turkish educational institutions
    /\.ac\.ae$/i,                 # UAE academic institutions
    /\.edu\.sa$/i,                # Saudi Arabian educational institutions
    /\.ac\.eg$/i,                 # Egyptian academic institutions
    /\.edu\.mx$/i,                # Mexican educational institutions
    /\.ac\.cr$/i,                 # Costa Rican academic institutions
    /\.edu\.co$/i,                # Colombian educational institutions
    /\.ac\.cl$/i,                 # Chilean academic institutions
    /\.edu\.ar$/i,                # Argentinian educational institutions
    /\.ac\.at$/i,                 # Austrian academic institutions
    /\.edu\.be$/i,                # Belgian educational institutions
    /\.ac\.be$/i,                 # Belgian academic institutions (alternative)
    /\.edu\.ch$/i,                # Swiss educational institutions
    /\.ac\.cz$/i,                 # Czech academic institutions
    /\.edu\.de$/i,                # German educational institutions
    /\.ac\.dk$/i,                 # Danish academic institutions
    /\.edu\.es$/i,                # Spanish educational institutions
    /\.ac\.fi$/i,                 # Finnish academic institutions
    /\.edu\.fr$/i,                # French educational institutions
    /\.ac\.gr$/i,                 # Greek academic institutions
    /\.edu\.hu$/i,                # Hungarian educational institutions
    /\.ac\.ie$/i,                 # Irish academic institutions
    /\.edu\.it$/i,                # Italian educational institutions
    /\.ac\.lv$/i,                 # Latvian academic institutions
    /\.edu\.lt$/i,                # Lithuanian educational institutions
    /\.ac\.lu$/i,                 # Luxembourg academic institutions
    /\.edu\.mt$/i,                # Maltese educational institutions
    /\.ac\.nl$/i,                 # Dutch academic institutions
    /\.edu\.no$/i,                # Norwegian educational institutions
    /\.ac\.pl$/i,                 # Polish academic institutions
    /\.edu\.pt$/i,                # Portuguese educational institutions
    /\.ac\.ro$/i,                 # Romanian academic institutions
    /\.edu\.ru$/i,                # Russian educational institutions
    /\.ac\.se$/i,                 # Swedish academic institutions
    /\.edu\.si$/i,                # Slovenian educational institutions
    /\.ac\.sk$/i,                 # Slovak academic institutions
    /\.edu\.ua$/i                 # Ukrainian educational institutions
  ].freeze

  def self.valid_university_email?(email)
    return false unless email.present?
    return false unless email.match?(URI::MailTo::EMAIL_REGEXP)

    domain = extract_domain(email)
    return false unless domain.present?

    # Check against configured patterns first
    configured_patterns = get_configured_patterns
    return true if configured_patterns.any? { |pattern| domain.match?(pattern) }

    # Fall back to default patterns if no custom configuration
    if configured_patterns.empty?
      return DEFAULT_UNIVERSITY_PATTERNS.any? { |pattern| domain.match?(pattern) }
    end

    false
  end

  def self.extract_domain(email)
    email.split('@').last&.downcase
  end

  def self.get_configured_patterns
    domains_setting = SiteSetting.student_verification_allowed_university_domains
    return [] unless domains_setting.present?

    domains_setting.split('|').map do |pattern|
      begin
        # If the pattern doesn't start with a dot, assume it's a domain ending
        pattern = pattern.strip
        if pattern.start_with?('.')
          Regexp.new("\\#{pattern}$", Regexp::IGNORECASE)
        else
          Regexp.new("\\.#{Regexp.escape(pattern)}$", Regexp::IGNORECASE)
        end
      rescue RegexpError => e
        Rails.logger.warn("Invalid university domain pattern: #{pattern} - #{e.message}")
        nil
      end
    end.compact
  end

  def self.validate_domain_patterns(patterns_string)
    return { valid: true, errors: [] } unless patterns_string.present?

    errors = []
    patterns = patterns_string.split('|')

    patterns.each_with_index do |pattern, index|
      pattern = pattern.strip
      next if pattern.empty?

      begin
        if pattern.start_with?('.')
          Regexp.new("\\#{pattern}$", Regexp::IGNORECASE)
        else
          Regexp.new("\\.#{Regexp.escape(pattern)}$", Regexp::IGNORECASE)
        end
      rescue RegexpError => e
        errors << "Pattern #{index + 1} (#{pattern}): #{e.message}"
      end
    end

    { valid: errors.empty?, errors: errors }
  end

  def self.get_example_emails_for_domain(domain)
    [
      "student@#{domain}",
      "john.doe@#{domain}",
      "jane.smith@#{domain}"
    ]
  end

  def self.suggest_corrections(email)
    return [] unless email.present?

    domain = extract_domain(email)
    return [] unless domain.present?

    suggestions = []

    # Common typos and corrections
    corrections = {
      'gmail.com' => ['university.edu', 'college.edu'],
      'yahoo.com' => ['university.edu', 'college.edu'],
      'hotmail.com' => ['university.edu', 'college.edu'],
      'outlook.com' => ['university.edu', 'college.edu'],
      'edu.com' => ['edu'],
      'ac.uk.com' => ['ac.uk'],
      'edu.au.com' => ['edu.au']
    }

    if corrections[domain]
      username = email.split('@').first
      corrections[domain].each do |suggested_domain|
        suggestions << "#{username}@#{suggested_domain}"
      end
    end

    suggestions
  end
end
