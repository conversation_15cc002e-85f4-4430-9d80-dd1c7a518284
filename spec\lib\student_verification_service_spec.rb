# frozen_string_literal: true

require 'rails_helper'

RSpec.describe StudentVerificationService do
  let(:user) { Fabricate(:user) }
  let(:service) { StudentVerificationService.new(user) }

  describe '.ensure_grace_period_group_exists' do
    context 'when group does not exist' do
      it 'creates the grace_period_users group' do
        expect(Group.find_by(name: 'grace_period_users')).to be_nil
        
        group = StudentVerificationService.ensure_grace_period_group_exists
        
        expect(group).to be_present
        expect(group.name).to eq('grace_period_users')
        expect(group.title).to eq('Students in Grace Period')
        expect(group.visibility_level).to eq(Group.visibility_levels[:staff])
      end
    end

    context 'when group already exists' do
      let!(:existing_group) { Fabricate(:group, name: 'grace_period_users') }

      it 'returns the existing group' do
        group = StudentVerificationService.ensure_grace_period_group_exists
        
        expect(group).to eq(existing_group)
      end
    end

    context 'when group creation fails' do
      before do
        allow(Group).to receive(:create!).and_raise(ActiveRecord::RecordInvalid.new(Group.new))
        allow(Group).to receive(:find_by).with(name: 'grace_period_users').and_return(nil)
      end

      it 'handles the error gracefully' do
        expect(Rails.logger).to receive(:error).with(/Failed to create grace_period_users group/)
        
        group = StudentVerificationService.ensure_grace_period_group_exists
        
        expect(group).to be_nil
      end
    end
  end

  describe '.add_user_to_grace_period_group' do
    context 'when group exists' do
      let!(:grace_period_group) { Fabricate(:group, name: 'grace_period_users') }

      it 'adds the user to the group' do
        expect(grace_period_group.users).not_to include(user)
        
        result = StudentVerificationService.add_user_to_grace_period_group(user)
        
        expect(result).to eq(true)
        expect(grace_period_group.reload.users).to include(user)
      end

      it 'logs success message' do
        expect(Rails.logger).to receive(:info).with(/Added user #{user.username}/)
        
        StudentVerificationService.add_user_to_grace_period_group(user)
      end
    end

    context 'when group does not exist' do
      it 'creates the group and adds the user' do
        expect(Group.find_by(name: 'grace_period_users')).to be_nil
        
        result = StudentVerificationService.add_user_to_grace_period_group(user)
        
        expect(result).to eq(true)
        group = Group.find_by(name: 'grace_period_users')
        expect(group.users).to include(user)
      end
    end

    context 'when adding user fails' do
      let!(:grace_period_group) { Fabricate(:group, name: 'grace_period_users') }

      before do
        allow(grace_period_group).to receive(:add).and_raise(StandardError.new("Test error"))
        allow(Group).to receive(:find_by).with(name: 'grace_period_users').and_return(grace_period_group)
      end

      it 'handles the error gracefully' do
        expect(Rails.logger).to receive(:error).with(/Failed to add user #{user.username}/)
        
        result = StudentVerificationService.add_user_to_grace_period_group(user)
        
        expect(result).to eq(false)
      end
    end
  end

  describe '.remove_user_from_grace_period_group' do
    let!(:grace_period_group) { Fabricate(:group, name: 'grace_period_users') }

    context 'when user is in the group' do
      before do
        grace_period_group.add(user)
      end

      it 'removes the user from the group' do
        expect(grace_period_group.users).to include(user)
        
        result = StudentVerificationService.remove_user_from_grace_period_group(user)
        
        expect(result).to eq(true)
        expect(grace_period_group.reload.users).not_to include(user)
      end

      it 'logs success message' do
        expect(Rails.logger).to receive(:info).with(/Removed user #{user.username}/)
        
        StudentVerificationService.remove_user_from_grace_period_group(user)
      end
    end

    context 'when group does not exist' do
      before do
        grace_period_group.destroy
      end

      it 'logs warning and returns false' do
        expect(Rails.logger).to receive(:warn).with(/grace_period_users group not found/)
        
        result = StudentVerificationService.remove_user_from_grace_period_group(user)
        
        expect(result).to eq(false)
      end
    end

    context 'when removing user fails' do
      before do
        grace_period_group.add(user)
        allow(grace_period_group).to receive(:remove).and_raise(StandardError.new("Test error"))
        allow(Group).to receive(:find_by).with(name: 'grace_period_users').and_return(grace_period_group)
      end

      it 'handles the error gracefully' do
        expect(Rails.logger).to receive(:error).with(/Failed to remove user #{user.username}/)
        
        result = StudentVerificationService.remove_user_from_grace_period_group(user)
        
        expect(result).to eq(false)
      end
    end
  end

  describe '#initiate_verification' do
    let(:email) { '<EMAIL>' }

    before do
      allow(UniversityEmailValidator).to receive(:valid_university_email?).and_return(true)
      allow(service).to receive(:send_verification_email)
    end

    it 'adds user to grace period group' do
      expect(StudentVerificationService).to receive(:add_user_to_grace_period_group).with(user)
      
      service.initiate_verification(email)
    end
  end
end
