# frozen_string_literal: true

class CreateStudentVerificationIndexes < ActiveRecord::Migration[7.0]
  def up
    # Add indexes for better performance on custom field queries
    # These indexes will help with verification status lookups and cleanup jobs
    
    execute <<~SQL
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_custom_fields_verification_email
      ON user_custom_fields (user_id, value)
      WHERE name = 'university_email_for_verification';
    SQL

    execute <<~SQL
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_custom_fields_verification_status
      ON user_custom_fields (user_id, value)
      WHERE name = 'verification_status';
    SQL

    execute <<~SQL
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_custom_fields_verification_deadline
      ON user_custom_fields (user_id, value)
      WHERE name = 'verification_deadline';
    SQL

    execute <<~SQL
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_custom_fields_token_expiry
      ON user_custom_fields (user_id, value)
      WHERE name = 'email_verification_token_expires_at';
    SQL
  end

  def down
    execute "DROP INDEX CONCURRENTLY IF EXISTS idx_user_custom_fields_verification_email;"
    execute "DROP INDEX CONCURRENTLY IF EXISTS idx_user_custom_fields_verification_status;"
    execute "DROP INDEX CONCURRENTLY IF EXISTS idx_user_custom_fields_verification_deadline;"
    execute "DROP INDEX CONCURRENTLY IF EXISTS idx_user_custom_fields_token_expiry;"
  end
end
